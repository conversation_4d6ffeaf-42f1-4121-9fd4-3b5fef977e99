<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>MD5CLUB - <PERSON><PERSON> hướng dẫ dẫn cài đặt</title>

	<!--http://www.html5rocks.com/en/mobile/mobifying/-->
	<meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>

	<!--https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html-->
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
	<meta name="format-detection" content="telephone=no">

	<!-- force webkit on 360 -->
	<meta name="renderer" content="webkit"/>
	<meta name="force-rendering" content="webkit"/>
	<!-- force edge on IE -->
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
	<meta name="msapplication-tap-highlight" content="no">

	<!-- force full screen on some browser -->
	<meta name="full-screen" content="yes"/>
	<meta name="x5-fullscreen" content="true"/>
	<meta name="360-fullscreen" content="true"/>
	
	<!-- force screen orientation on some browser -->
	<meta name="screen-orientation" content=""/>
	<meta name="x5-orientation" content="">

	<!--fix fireball/issues/3568 -->
	<!--<meta name="browsermode" content="application">-->
	<meta name="x5-page-mode" content="app">

	<link rel="shortcut icon" href="/favicon.ico">
	<style type="text/css">
* {
  box-sizing: border-box;
}
html, body {
	background-color: #650000;
	color: #424242;
	font-weight:400;
	margin: 0;
	padding: 0;
}
html,
body,
input,
select,
textarea {
	font-family: Roboto, Helvetica, Arial, "Liberation Sans", sans-serif;
	font-size: 16px;
}
.container {
  margin: 10px auto;
  max-width: 750px;
  padding: 10px;
}
.container + .container {
	padding: 0 10px 10px 10px;
}
@media (max-width: 750px) {
	.container {
	  	margin: 0 auto;
	}
}
.card {
  position: relative;
  background-color: #fff;
  width: 100%;
  margin: 0;
  padding: 10px;
}
.z-depth {
  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 1px 5px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 1px 5px 0 rgba(0, 0, 0, 0.2);
}
	</style>
</head>
<body>
	<div class="container">
		<div class="card z-depth">
			<h1><center style="color: #ff0000">MD5CLUB</center></h1>
			<h4><center>Hướng dẫn cài đặt Game trên thiết bị IOS</center></h4>
			<strong style="color: #ff6226;">Bước 1</strong>: Mở trình duyệt <strong>Safari</strong> và truy cập vào địa chỉ <strong>https://md5.club</strong>.
		</div>
	</div>
	<div class="container">
		<div class="card z-depth">
			<strong style="color: #ff6226;">Bước 2</strong>: Sau khi truy cập bạn nhấn vào biểu tượng <strong>Chia sẻ</strong> trên trình duyệt.<br />
			<div class="card"><img class="z-depth" src="/images/helper/ios/B01.PNG" style="width: 100%; border-radius: 10px;"></div>
		</div>
	</div>
	<div class="container">
		<div class="card z-depth">
			<strong style="color: #ff6226;">Bước 3</strong>: Chọn <strong>Thêm vào màn hình chính</strong>.<br />
			<div class="card"><img class="z-depth" src="/images/helper/ios/B02.PNG" style="width: 100%; border-radius: 10px;"></div>
		</div>
	</div>
	<div class="container">
		<div class="card z-depth">
			<strong style="color: #ff6226;">Bước 4</strong>: Sau đó chọn <strong>Thêm</strong><br />
			<div class="card"><img class="z-depth" src="/images/helper/ios/B03.PNG" style="width: 100%; border-radius: 10px;"></div>
		</div>
	</div>
	<div class="container">
		<div class="card z-depth">
			<center><strong style="color: #11b158;">Chúc mừng bạn đã cài đặt thành công.</strong></center><br/>
			<div class="card"><img class="z-depth" src="/images/helper/ios/B04.PNG" style="width: 100%; border-radius: 10px;"></div>
		</div>
	</div>
</body>
</html>
