@echo off
title GO88 Quick Start (No Dependencies Check)
color 0B
echo.
echo ========================================
echo       GO88 QUICK START MODE
echo ========================================
echo.
echo This will start the server without checking dependencies.
echo Use this if you're having npm/installation issues.
echo.

REM Kill any existing processes
echo [1/3] Cleaning up existing processes...
taskkill /IM node.exe /F >nul 2>&1

REM Free up port
echo [2/3] Freeing port 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| find "8080" ^| find "LISTENING"') do (
    taskkill /PID %%a /F >nul 2>&1
)

REM Start server directly
echo [3/3] Starting server in quick mode...
echo.
echo ========================================
echo Server starting on: http://localhost:8080
echo Press Ctrl+C to stop
echo ========================================
echo.

node server.js

echo.
echo ========================================
echo Server stopped!
echo ========================================
pause
