# Hướng dẫn khắc phục sự cố GO88 Game Server

## Vấn đề: Không thể khởi động server sau khi chat crash

### Nguyên nhân:
1. **File run.bat có lệnh sai** - `node server start` thay vì `node server.js`
2. **Process Node.js bị treo** - Server crash nhưng process vẫn chạy
3. **Port 8080 bị chiếm dụng** - Process cũ chưa được giải phóng
4. **Dependencies bị lỗi** - Thiếu hoặc lỗi module

### Giải pháp:

#### Bước 1: Dọn dẹp hệ thống
```batch
# Chạy file cleanup.bat để dọn dẹp
cleanup.bat
```

#### Bước 2: Khởi động server
```batch
# Sử dụng file start.bat mới (an toàn hơn)
start.bat

# Hoặc sử dụng run.bat đã được sửa
run.bat

# Hoặc chạy trực tiếp
node server.js
```

#### Bước 3: <PERSON><PERSON><PERSON> tra lỗi thường gặp

**Lỗi: "Cannot find module 'helmet'"**
```bash
npm install helmet
```

**Lỗi: "Port 8080 is already in use"**
```bash
# Windows
netstat -ano | findstr :8080
taskkill /PID <PID_NUMBER> /F

# Hoặc chạy cleanup.bat
```

**Lỗi: "MongoDB connection failed"**
```bash
# Kiểm tra MongoDB đang chạy
net start MongoDB

# Hoặc khởi động MongoDB
mongod --dbpath C:\data\db
```

**Lỗi: "Cannot read property of undefined"**
- Đây là lỗi đã được sửa trong chat system
- Đảm bảo bạn đã cập nhật code mới nhất

## Các file khởi động mới:

### 1. `run.bat` (Đã sửa)
- Lệnh chính xác: `node server.js`
- Kiểm tra Node.js installation
- Thông báo lỗi rõ ràng

### 2. `start.bat` (Mới)
- Kiểm tra đầy đủ hệ thống
- Tự động cài đặt dependencies
- Dọn dẹp port conflicts
- Thông báo chi tiết

### 3. `cleanup.bat` (Mới)
- Dọn dẹp processes bị treo
- Giải phóng ports
- Xóa temporary files
- Reset system state

## Monitoring và Debug:

### Kiểm tra server status:
```bash
# Kiểm tra port
netstat -ano | findstr :8080

# Kiểm tra processes
tasklist | findstr node.exe

# Kiểm tra logs
# Server sẽ hiển thị logs trực tiếp trong console
```

### Debug chat system:
- Tất cả lỗi chat đã được sửa
- Error logs sẽ hiển thị trong console
- Server không crash khi có lỗi chat

### Performance monitoring:
```bash
# Memory usage
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE

# CPU usage
wmic process where name="node.exe" get processid,percentprocessortime
```

## Backup và Recovery:

### Backup database:
```bash
mongodump --db CLUB3333 --out backup/
```

### Restore database:
```bash
mongorestore --db CLUB3333 backup/CLUB3333/
```

### Backup code:
```bash
# Tạo backup trước khi update
xcopy /E /I . backup_$(date +%Y%m%d)
```

## Cấu hình Production:

### Sử dụng PM2 (Khuyến nghị):
```bash
# Cài đặt PM2
npm install -g pm2

# Khởi động với PM2
pm2 start server.js --name "go88-game"

# Auto restart on system boot
pm2 startup
pm2 save

# Monitor
pm2 status
pm2 logs go88-game
```

### Environment Variables:
```bash
# Tạo file .env
copy .env.example .env

# Cập nhật thông số production
NODE_ENV=production
PORT=8080
MONGODB_URL=mongodb://localhost:27017
DB_NAME=CLUB3333
```

## Liên hệ hỗ trợ:

Nếu vẫn gặp vấn đề:
1. Chạy `cleanup.bat` trước
2. Kiểm tra logs trong console
3. Đảm bảo MongoDB đang chạy
4. Kiểm tra port 8080 không bị chiếm dụng
5. Cài đặt lại dependencies: `npm install`

**Server đã được sửa lỗi chat và sẵn sàng hoạt động ổn định!**
