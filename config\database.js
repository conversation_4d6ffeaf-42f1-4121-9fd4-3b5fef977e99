module.exports = {
	'url': process.env.MONGODB_URL || 'mongodb://127.0.0.1:27017',
	'options': {
		'dbName': process.env.DB_NAME || 'CLUB3333', // red
		//'dbName': 'GAME', // red
		//'dbName': 'admin', // red
                //'dbName': 'vn11022021', // red
		'useNewUrlParser': true,
		'useUnifiedTopology': true,
		'authSource': process.env.DB_AUTH_SOURCE || 'admin',
		'ssl': process.env.DB_SSL === 'true' || false,

		// Performance optimization for production
		'maxPoolSize': 50, // Maximum number of connections
		'minPoolSize': 5,  // Minimum number of connections
		'maxIdleTimeMS': 30000, // Close connections after 30 seconds of inactivity
		'serverSelectionTimeoutMS': 5000, // How long to try selecting a server
		'socketTimeoutMS': 45000, // How long a send or receive on a socket can take
		'connectTimeoutMS': 10000, // How long to wait for a connection to be established
		'heartbeatFrequencyMS': 10000, // How often to check server status
		'retryWrites': true, // <PERSON><PERSON> writes on network errors
		'retryReads': true, // Retry reads on network errors
		'bufferMaxEntries': 0, // Disable mongoose buffering
		'bufferCommands': false, // Disable mongoose buffering
		//'autoIndex': false, // Don't build indexes in production
	},
};
