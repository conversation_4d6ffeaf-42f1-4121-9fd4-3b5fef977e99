{"name": "RED79", "version": "3.0.0", "main": "index.js", "description": "...", "scripts": {"start": "node server", "dev": "NODE_ENV=development ./node_modules/.bin/webpack --watch", "build": "NODE_ENV=production ./node_modules/.bin/webpack", "webpack": "set NODE_ENV=production ./node_modules/.bin/webpack", "test": "jest", "test:watch": "npm test -- --watch"}, "engines": {"node": "6.10.0", "npm": "3.10.10"}, "dependencies": {"@babel/core": "^7.1.0", "@babel/preset-env": "^7.1.0", "axios": "^0.19.2", "babel": "^6.23.0", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-polyfill": "^6.3.14", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.5.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-1": "^6.1.18", "body-parser": "^1.15.2", "browser_fingerprint": "^1.0.4", "bugsnag": "^2.0.1", "bugsnag-js": "^4.1.1", "bugsnag-react": "^1.0.0", "chai": "^3.5.0", "chai-jquery": "^2.0.0", "cleave.js": "^1.4.4", "cookie-parser": "^1.4.3", "css-loader": "^0.26.1", "decimal.js-light": "^2.3.1", "ejs": "^2.5.6", "errorhandler": "^1.5.0", "everyauth": "^0.4.9", "express": "~3.1.0", "express-fingerprint": "^1.1.2", "express-session": "^1.14.2", "file-loader": "^0.9.0", "fs-extra": "^1.0.0", "geoip-lite": "^1.2.0", "history": "^3.3.0", "image-webpack-loader": "^3.0.0", "immutable": "^3.8.1", "ipware": "^1.0.0", "jquery": "^2.2.1", "jsdom": "^8.1.0", "keyboardjs": "^2.3.3", "less-middleware": "^2.2.0", "lodash": "^4.17.19", "method-override": "^2.3.7", "mobile-detect": "^1.3.6", "mocha": "^2.4.5", "moment": "^2.17.1", "moment-timezone": "^0.5.21", "morgan": "^1.7.0", "numeral": "^2.0.1", "optimize-js-plugin": "0.0.4", "pm2": "^2.4.6", "prop-types": "^15.6.2", "react": "^16.2.0", "react-addons-shallow-compare": "^15.6.3", "react-addons-test-utils": "^0.14.7", "react-dom": "^16.2.0", "react-helmet": "^5.2.1", "react-intl-redux": "^0.1.0", "react-modal": "^1.6.1", "react-number-format": "^3.6.2", "react-player": "^1.5.0", "react-redux": "^4.4.6", "react-redux-i18n": "https://registry.npmjs.org/react-redux-i18n/-/react-redux-i18n-0.1.1.tgz", "react-router": "^3.2.1", "react-router-redux": "^4.0.7", "redux": "^3.0.4", "redux-devtools": "^3.4.1", "redux-form": "^5.3.3", "redux-thunk": "^2.1.0", "request-ip": "^2.0.1", "reselect": "^4.0.0", "serve-favicon": "^2.3.2", "shallow-compare": "^1.2.2", "style-loader": "^0.13.1", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2", "webpack": "^2.7.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^1.16.5"}, "devDependencies": {"jest": "^23.5.0"}}