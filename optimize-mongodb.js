// Script tối ưu hóa MongoDB cho production
const mongoose = require('mongoose');

// Kết nối database
const dbConfig = require('./config/database');
mongoose.connect(dbConfig.url, dbConfig.options);

async function optimizeDatabase() {
    try {
        console.log('🚀 Bắt đầu tối ưu hóa MongoDB...\n');
        
        const db = mongoose.connection.db;
        
        // 1. Tạo indexes cho các collection quan trọng
        console.log('📊 Tạo indexes...');
        
        // UserInfo indexes
        await db.collection('userinfos').createIndex({ 'id': 1 }, { background: true });
        await db.collection('userinfos').createIndex({ 'name': 1 }, { background: true });
        await db.collection('userinfos').createIndex({ 'phone': 1 }, { background: true });
        console.log('✅ UserInfo indexes created');
        
        // TaiXiu indexes
        await db.collection('taixius').createIndex({ 'uid': 1, 'phien': 1 }, { background: true });
        await db.collection('taixius').createIndex({ 'time': -1 }, { background: true });
        console.log('✅ TaiXiu indexes created');
        
        // Chat indexes
        await db.collection('txchats').createIndex({ 'time': -1 }, { background: true });
        await db.collection('txchats').createIndex({ 'uid': 1 }, { background: true });
        console.log('✅ Chat indexes created');
        
        // Message indexes
        await db.collection('messages').createIndex({ 'uid': 1, 'time': -1 }, { background: true });
        console.log('✅ Message indexes created');
        
        // 2. Tối ưu hóa collection settings
        console.log('\n⚙️  Tối ưu hóa collection settings...');
        
        // Set read preference to secondary for read-heavy operations
        await db.collection('txchats').aggregate([
            { $indexStats: {} }
        ]).toArray();
        
        // 3. Dọn dẹp dữ liệu cũ
        console.log('\n🧹 Dọn dẹp dữ liệu cũ...');
        
        // Xóa chat cũ hơn 7 ngày
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const chatResult = await db.collection('txchats').deleteMany({
            'time': { $lt: sevenDaysAgo }
        });
        console.log(`✅ Đã xóa ${chatResult.deletedCount} chat cũ`);
        
        // Xóa OTP cũ hơn 1 ngày
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const otpResult = await db.collection('otps').deleteMany({
            'date': { $lt: oneDayAgo }
        });
        console.log(`✅ Đã xóa ${otpResult.deletedCount} OTP cũ`);
        
        // 4. Compact collections
        console.log('\n📦 Compact collections...');
        try {
            await db.command({ compact: 'txchats' });
            await db.command({ compact: 'otps' });
            console.log('✅ Collections compacted');
        } catch (e) {
            console.log('⚠️  Compact failed (normal on some MongoDB versions)');
        }
        
        // 5. Hiển thị thống kê
        console.log('\n📈 Thống kê database:');
        const stats = await db.stats();
        console.log(`Database size: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`Index size: ${(stats.indexSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`Collections: ${stats.collections}`);
        console.log(`Indexes: ${stats.indexes}`);
        
        // 6. Kiểm tra connection pool
        console.log('\n🔗 Connection pool status:');
        console.log(`Ready state: ${mongoose.connection.readyState}`);
        console.log(`Host: ${mongoose.connection.host}`);
        console.log(`Port: ${mongoose.connection.port}`);
        
        console.log('\n✅ Tối ưu hóa MongoDB hoàn thành!');
        
    } catch (error) {
        console.error('❌ Lỗi tối ưu hóa:', error);
    } finally {
        mongoose.connection.close();
    }
}

// Chạy tối ưu hóa
optimizeDatabase();
