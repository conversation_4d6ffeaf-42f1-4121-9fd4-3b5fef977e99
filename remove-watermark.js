// Script để xóa watermark "SOURCE SHARE BY NXT TELEGRAM @setupgamebai"
const mongoose = require('mongoose');
const ThongBao = require('./app/Models/Title');

// Kết nối database
mongoose.connect('mongodb://127.0.0.1:27017/CLUB3333', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

async function removeWatermark() {
    try {
        console.log('Đang tìm kiếm watermark text...');
        
        // Tìm tất cả thông báo có chứa text watermark
        const watermarkTexts = [
            'SOURCE SHARE BY NXT TELEGRAM @setupgamebai',
            'setupgamebai',
            'NXT TELEGRAM',
            'SOURCE SHARE BY NXT'
        ];
        
        for (let watermarkText of watermarkTexts) {
            console.log(`Tìm kiếm: ${watermarkText}`);
            
            // Tìm và cập nhật thongbao1
            let result1 = await Thong<PERSON>ao.updateMany(
                { thongbao1: { $regex: watermarkText, $options: 'i' } },
                { $set: { thongbao1: 'GO88 Game xanh chín uy tín' } }
            );
            
            // Tìm và cập nhật thongbao2
            let result2 = await ThongBao.updateMany(
                { thongbao2: { $regex: watermarkText, $options: 'i' } },
                { $set: { thongbao2: 'Chào mừng bạn đến với GO88!' } }
            );
            
            // Tìm và cập nhật thongbao3
            let result3 = await ThongBao.updateMany(
                { thongbao3: { $regex: watermarkText, $options: 'i' } },
                { $set: { thongbao3: 'Game bài đổi thưởng uy tín số 1' } }
            );
            
            // Tìm và cập nhật hienthitb
            let result4 = await ThongBao.updateMany(
                { hienthitb: { $regex: watermarkText, $options: 'i' } },
                { $set: { hienthitb: 'GO88 - Game bài đổi thưởng uy tín' } }
            );
            
            if (result1.modifiedCount > 0) {
                console.log(`✅ Đã xóa ${result1.modifiedCount} watermark trong thongbao1`);
            }
            if (result2.modifiedCount > 0) {
                console.log(`✅ Đã xóa ${result2.modifiedCount} watermark trong thongbao2`);
            }
            if (result3.modifiedCount > 0) {
                console.log(`✅ Đã xóa ${result3.modifiedCount} watermark trong thongbao3`);
            }
            if (result4.modifiedCount > 0) {
                console.log(`✅ Đã xóa ${result4.modifiedCount} watermark trong hienthitb`);
            }
        }
        
        // Hiển thị tất cả thông báo hiện tại
        console.log('\n📋 Thông báo hiện tại trong database:');
        const allThongBao = await ThongBao.find({});
        allThongBao.forEach((tb, index) => {
            console.log(`\n--- Thông báo ${index + 1} ---`);
            console.log(`thongbao1: ${tb.thongbao1}`);
            console.log(`thongbao2: ${tb.thongbao2}`);
            console.log(`thongbao3: ${tb.thongbao3}`);
            console.log(`hienthitb: ${tb.hienthitb}`);
            console.log(`active: ${tb.active}`);
        });
        
        console.log('\n✅ Hoàn thành kiểm tra và xóa watermark!');
        
    } catch (error) {
        console.error('❌ Lỗi:', error);
    } finally {
        mongoose.connection.close();
    }
}

// Chạy script
removeWatermark();
