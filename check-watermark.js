// Script kiểm tra watermark trong database
const mongoose = require('mongoose');

// Kết nối database
mongoose.connect('mongodb://127.0.0.1:27017/CLUB3333', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// Schema ThongBao
const ThongBaoSchema = new mongoose.Schema({
    thongbao1: {type: String, default: ''},
    thongbao2: {type: String, default: ''}, 
    thongbao3: {type: String, default: ''}, 
    hienthitb: {type: String, default: ''}, 
    active: {type: String, default: '1'},
});

const ThongBao = mongoose.model('ThongBao', ThongBaoSchema);

async function checkWatermark() {
    try {
        console.log('🔍 Đang kiểm tra database cho watermark...\n');
        
        // Lấy tất cả thông báo
        const allThongBao = await Thong<PERSON><PERSON>.find({});
        
        if (allThongBao.length === 0) {
            console.log('❌ Không tìm thấy thông báo nào trong database');
            return;
        }
        
        console.log(`📋 Tìm thấy ${allThongBao.length} thông báo:\n`);
        
        let foundWatermark = false;
        
        allThongBao.forEach((tb, index) => {
            console.log(`--- Thông báo ${index + 1} ---`);
            console.log(`ID: ${tb._id}`);
            console.log(`thongbao1: "${tb.thongbao1}"`);
            console.log(`thongbao2: "${tb.thongbao2}"`);
            console.log(`thongbao3: "${tb.thongbao3}"`);
            console.log(`hienthitb: "${tb.hienthitb}"`);
            console.log(`active: ${tb.active}`);
            
            // Kiểm tra watermark
            const watermarkKeywords = ['setupgamebai', 'NXT', 'SOURCE SHARE'];
            const allText = `${tb.thongbao1} ${tb.thongbao2} ${tb.thongbao3} ${tb.hienthitb}`.toLowerCase();
            
            watermarkKeywords.forEach(keyword => {
                if (allText.includes(keyword.toLowerCase())) {
                    console.log(`🚨 FOUND WATERMARK: "${keyword}" trong thông báo ${index + 1}`);
                    foundWatermark = true;
                }
            });
            
            console.log('');
        });
        
        if (!foundWatermark) {
            console.log('✅ Không tìm thấy watermark trong database');
        } else {
            console.log('⚠️  Tìm thấy watermark! Chạy remove-watermark.js để xóa');
        }
        
    } catch (error) {
        console.error('❌ Lỗi kết nối database:', error.message);
        console.log('\n💡 Hướng dẫn khắc phục:');
        console.log('1. Đảm bảo MongoDB đang chạy: net start MongoDB');
        console.log('2. Kiểm tra tên database trong config/database.js');
        console.log('3. Chạy server trước để tạo database');
    } finally {
        mongoose.connection.close();
    }
}

// Chạy script
checkWatermark();
