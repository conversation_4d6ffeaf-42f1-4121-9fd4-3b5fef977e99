@echo off
title Install Helmet Security Module
echo.
echo ========================================
echo   INSTALLING HELMET SECURITY MODULE
echo ========================================
echo.

echo [1/3] Checking Node.js version...
node --version
if errorlevel 1 (
    echo [ERROR] Node.js not found!
    pause
    exit /b 1
)

echo.
echo [2/3] Installing helmet module...
echo This may take a few minutes...
echo.

REM Try different methods to install helmet
echo Trying method 1: npm install helmet
npm install helmet
if not errorlevel 1 (
    echo [SUCCESS] Helmet installed successfully!
    goto :enable_helmet
)

echo.
echo Method 1 failed, trying method 2: npm install helmet --no-optional
npm install helmet --no-optional
if not errorlevel 1 (
    echo [SUCCESS] Helmet installed successfully!
    goto :enable_helmet
)

echo.
echo Method 2 failed, trying method 3: npm install helmet --legacy-peer-deps
npm install helmet --legacy-peer-deps
if not errorlevel 1 (
    echo [SUCCESS] Helmet installed successfully!
    goto :enable_helmet
)

echo.
echo [WARNING] All installation methods failed.
echo Server will run without helmet security module.
echo This is safe for development but not recommended for production.
echo.
pause
exit /b 0

:enable_helmet
echo.
echo [3/3] Enabling helmet in server.js...
echo.
echo Helmet has been installed successfully!
echo You can now enable it in server.js by uncommenting the helmet lines.
echo.
echo ========================================
echo Installation completed!
echo You can now start the server safely.
echo ========================================
pause
