
var RoyAl_red = require('../../../Models/LichSu_Cuoc');
var RoyAl_xu  = require('../../../Models/RoyAl/RoyAl_xu');

var UserInfo     = require('../../../Models/UserInfo');

module.exports = function(client, data){
	var red    = !!data;   // Loại tiền (Red: true, Xu: false)
	if (red) {
		RoyAl_red.find({dichvu:'Royal', type:{$gte:1}}, 'name win bet time type', {sort:{'_id':-1}, limit: 50}, function(err, result) {
			Promise.all(result.map(function(obj){
				obj = obj._doc;
				delete obj.__v;
				delete obj._id;
				return obj;
			}))
			.then(function(arrayOfResults) {
				client.red({royal:{top:arrayOfResults}});
			})
		});
	}else{
		RoyAl_xu.find({type:{$gte:1}}, 'name win bet time type', {sort:{'_id':-1}, limit: 50}, function(err, result) {
			Promise.all(result.map(function(obj){
				obj = obj._doc;
				delete obj.__v;
				delete obj._id;
				return obj;
			}))
			.then(function(arrayOfResults) {
				client.red({royal:{top:arrayOfResults}});
			})
		});
	}
};
