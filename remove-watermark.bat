@echo off
title Remove Watermark Tool
color 0E
echo.
echo ========================================
echo     GO88 WATERMARK REMOVAL TOOL
echo ========================================
echo.

echo [1/4] Checking MongoDB connection...
echo.

REM Check if MongoDB is running
net start | find "MongoDB" >nul
if errorlevel 1 (
    echo [WARNING] MongoDB service not found
    echo [INFO] Trying to start MongoDB...
    net start MongoDB >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Cannot start MongoDB
        echo [INFO] Please start MongoDB manually or check installation
        echo.
        pause
        exit /b 1
    )
) else (
    echo [OK] MongoDB is running
)

echo.
echo [2/4] Checking for watermark in database...
echo.
node check-watermark.js

echo.
echo [3/4] Do you want to remove the watermark? (Y/N)
set /p choice="Enter your choice: "

if /i "%choice%"=="Y" (
    echo.
    echo [4/4] Removing watermark...
    echo.
    node remove-watermark.js
    echo.
    echo ========================================
    echo Watermark removal completed!
    echo Please restart the server to see changes.
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Operation cancelled by user.
    echo ========================================
)

echo.
pause
