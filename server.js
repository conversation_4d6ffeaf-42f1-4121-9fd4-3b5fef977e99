﻿
require('dotenv').config();
var cors = require('cors');
let Telegram      = require('node-telegram-bot-api');
let TelegramToken = process.env.TELEGRAM_BOT_TOKEN || '**********************************************';
let TelegramBot   = new Telegram(TelegramToken, {polling: true});
let fs 			  = require('fs');
//let https     	  = require('https')
//let privateKey    = fs.readFileSync('./ssl/b86club.key', 'utf8');
//let certificate   = fs.readFileSync('./ssl/b86club.pem', 'utf8');
//let credentials   = {key: privateKey, cert: certificate};
let express       = require('express');
let compression   = require('compression');
let app           = express();
//let server 	  	  = https.createServer(credentials, app);

// Enable gzip compression for better performance
app.use(compression({
    level: 6, // Compression level (1-9, 6 is good balance)
    threshold: 1024, // Only compress responses > 1KB
    filter: function(req, res) {
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    }
}));

// Security middleware - helmet temporarily disabled for compatibility
// TODO: Install helmet with: npm install helmet
// app.use(helmet({
//     contentSecurityPolicy: false, // Disable CSP for WebSocket compatibility
//     crossOriginEmbedderPolicy: false
// }));

// CORS configuration - restrict origins in production
const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') :
    ['http://*************:8080', 'http://*************', 'http://localhost:3000', 'http://localhost:8080'];

app.use(cors({
    origin: process.env.NODE_ENV === 'production' ? allowedOrigins : '*',
    credentials: true,
    optionsSuccessStatus: 200,
    maxAge: 86400 // Cache preflight requests for 24 hours
}));

// Performance optimizations
app.set('trust proxy', 1); // Trust first proxy
app.disable('x-powered-by'); // Remove Express signature
let port       = process.env.PORT || 8080;
let expressWs  = require('express-ws')(app);
let bodyParser = require('body-parser');
var morgan = require('morgan');
// Setting & Connect to the Database
let configDB = require('./config/database');
let mongoose = require('mongoose');
require('mongoose-long')(mongoose); // INT 64bit
mongoose.set('useFindAndModify', false);
mongoose.set('useCreateIndex',   true);
mongoose.connect(configDB.url, configDB.options); // kết nối tới database
// cấu hình tài khoản admin mặc định và các dữ liệu mặc định
require('./config/admin');
// đọc dữ liệu from
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({extended:false}));
app.use(morgan('combined'));
app.set('view engine', 'ejs'); // chỉ định view engine là ejs
app.set('views', './views');   // chỉ định thư mục view
// Serve static html, js, css, and image files from the 'public' directory with caching
const oneYear = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds

app.use(express.static('public', {
    maxAge: process.env.NODE_ENV === 'production' ? oneYear : 0,
    etag: true,
    lastModified: true,
    setHeaders: function(res, path) {
        if (path.endsWith('.js') || path.endsWith('.css')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year
        } else if (path.endsWith('.png') || path.endsWith('.jpg') || path.endsWith('.ico')) {
            res.setHeader('Cache-Control', 'public, max-age=2592000'); // 30 days
        }
    }
}));
// server socket
let redT = expressWs.getWss();
process.redT = redT;
redT.telegram = TelegramBot;
global['redT'] = redT;
global['userOnline'] = 0;
require('./app/Helpers/socketUser')(redT); // Add function socket
require('./routerHttp')(app, redT);   // load các routes HTTP
require('./routerCMS')(app, redT);	//load routes CMS
require('./routerSocket')(app, redT); // load các routes WebSocket
require('./app/Cron/taixiu')(redT);   // Chạy game Tài Xỉu
require('./app/Cron/baucua')(redT);   // Chạy game Bầu Cua
require('./config/cron')();
require('./app/Telegram/Telegram')(redT); // Telegram Bot
app.listen(port, function() {
    console.log("Server listen on port ", port);
});