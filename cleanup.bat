@echo off
title GO88 Server Cleanup
color 0C
echo.
echo ========================================
echo       GO88 SERVER CLEANUP TOOL
echo ========================================
echo.

echo [1/4] Killing Node.js processes...
tasklist | find "node.exe" >nul
if not errorlevel 1 (
    taskkill /IM node.exe /F >nul 2>&1
    echo [OK] Node.js processes terminated
) else (
    echo [INFO] No Node.js processes found
)

echo [2/4] Freeing port 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| find "8080" ^| find "LISTENING"') do (
    taskkill /PID %%a /F >nul 2>&1
    echo [OK] Process using port 8080 terminated
)

echo [3/4] Cleaning temporary files...
if exist "*.log" del "*.log" >nul 2>&1
if exist "temp\*" del "temp\*" /Q >nul 2>&1

echo [4/4] Checking system status...
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo Cleanup completed!
echo You can now run start.bat safely.
echo ========================================
echo.
pause
