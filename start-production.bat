@echo off
title GO88 Production Server (IP: *************)
color 0A
echo.
echo ========================================
echo    GO88 PRODUCTION SERVER
echo    IP: *************:8080
echo ========================================
echo.

REM Set production environment variables
set NODE_ENV=production
set NODE_OPTIONS=--max-old-space-size=1024
set UV_THREADPOOL_SIZE=8

echo [1/5] Setting up production environment...
echo ✅ Memory limit: 1GB
echo ✅ Thread pool: 8 threads
echo ✅ Environment: Production
echo.

echo [2/5] Cleaning up existing processes...
taskkill /IM node.exe /F >nul 2>&1
timeout /t 2 /nobreak >nul
echo ✅ Old processes cleaned

echo.
echo [3/5] Checking MongoDB connection...
echo.

REM Try to connect to MongoDB
mongo --eval "db.stats()" --quiet >nul 2>&1
if errorlevel 1 (
    echo ⚠️  MongoDB not responding, trying to start...
    net start MongoDB >nul 2>&1
    timeout /t 3 /nobreak >nul
) else (
    echo ✅ MongoDB is running
)

echo.
echo [4/5] Optimizing database (cleaning old data)...
node optimize-db-simple.js

echo.
echo [5/5] Starting optimized server...
echo.
echo 🚀 Server will start on: http://*************:8080
echo 🌐 Local access: http://localhost:8080
echo 📊 Environment: Production
echo 💾 Memory limit: 1GB
echo ⚡ Optimized for performance
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start the server with optimizations
node server.js

REM If server stops, show message
echo.
echo ========================================
echo Server has stopped!
echo Check the messages above for any errors.
echo ========================================
pause
