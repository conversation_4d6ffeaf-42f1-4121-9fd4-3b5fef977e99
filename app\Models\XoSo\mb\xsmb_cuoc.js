
let mongoose = require('mongoose');

let Schema = new mongoose.Schema({
	name:      {type: String, required:true, index:true},      // ID người chơi
	date:      {type: String, required:true, index:true},      // cư<PERSON><PERSON>
	type:      {type: String},                                 // <PERSON><PERSON><PERSON> cược
	so:        {type: Array},                                  // Số chọn
	diem:      {type: Number, default: 0},                     // Số điểm
	thanhtoan: {type: Boolean, default: false},                // Trạng thái thanh toán (false: chưa, true: đã trả)
	cuoc:      {type: mongoose.Schema.Types.Long, default: 0}, // T<PERSON><PERSON> cược
	win:       {type: mongoose.Schema.Types.Long, default: 0}, // Tổng Thắng
	time:      {type: Date},                                   // thời gian
});

module.exports = mongoose.model('xsmb_cuoc', Schema);
