<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>RVIP.FUN Game Đổi Thưởng Số 1 VN</title>

	<!--http://www.html5rocks.com/en/mobile/mobifying/-->
	<meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>

	<!-- Add to homescreen -->
    <link rel="manifest" href="manifest.json">

	<!-- force webkit on 360 -->
	<meta name="renderer" content="webkit"/>
	<meta name="force-rendering" content="webkit"/>
	<!-- force edge on IE -->
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
	<meta name="msapplication-tap-highlight" content="no">

	<!-- force full screen on some browser -->
	<meta name="full-screen" content="yes"/>
	<meta name="x5-fullscreen" content="true"/>
	<meta name="360-fullscreen" content="true"/>
	
	<!-- force screen orientation on some browser -->
	<meta name="screen-orientation" content="landscape"/>
	<meta name="x5-orientation" content="landscape">

	<!--fix fireball/issues/3568 -->
	<meta name="browsermode" content="application">
	<meta name="x5-page-mode" content="app">

	<!-- Fallback to homescreen for Chrome <39 on Android -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="Material Design Lite">
    <link rel="icon" sizes="192x192" href="/images/icon.png">

	<!-- Add to homescreen for Safari on iOS -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="RVIP">
    <link rel="apple-touch-icon" href="/images/icon.png">
    <meta name="format-detection" content="telephone=no">

	<link rel="shortcut icon" href="/favicon.ico">

	<link rel="stylesheet" type="text/css" href="style.css"/>

</head>
<body>
	<canvas id="GameCanvas" oncontextmenu="event.preventDefault()" tabindex="0"></canvas>
	<div id="splash">
		<div id="preview-area">
			<div class="sk-folding-cube">
			<div class="textLoad"></div>
			</div>
		</div>
	</div>
<script src="/assets/js/debug.js"></script>
<script src="src/settings.js" charset="utf-8"></script>

<script src="main.js" charset="utf-8"></script>

<script type="text/javascript">
(function () {
		// open web debugger console
		if (typeof VConsole !== 'undefined') {
				window.vConsole = new VConsole();
		}

		var splash = document.getElementById('splash');
		splash.style.display = 'block';

		var cocos2d = document.createElement('script');
		cocos2d.async = true;
		cocos2d.src = window._CCSettings.debug ? 'cocos2d-js.js' : 'cocos2d-js-min.js';

		var engineLoaded = function () {
				document.body.removeChild(cocos2d);
				cocos2d.removeEventListener('load', engineLoaded, false);
				window.boot();
		};
		cocos2d.addEventListener('load', engineLoaded, false);
		document.body.appendChild(cocos2d);
})();
</script>
</body>
</html>
