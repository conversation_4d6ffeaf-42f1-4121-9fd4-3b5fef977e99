# Hướng dẫn triển khai lên Server Public IP

## 1. <PERSON><PERSON><PERSON> bị Server

### <PERSON><PERSON><PERSON> c<PERSON>u hệ thống:
- Ubuntu 20.04+ hoặc CentOS 7+
- Node.js 14+ 
- MongoDB 4.4+
- RAM: tối thiểu 2GB
- Disk: tối thiểu 20GB

### Cài đặt dependencies:
```bash
# Cài đặt Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Cài đặt MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Cài đặt PM2 để quản lý process
sudo npm install -g pm2
```

## 2. Cấu hình Database

### MongoDB Configuration:
```bash
# Khởi động MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Tạo user admin cho database
mongo
use admin
db.createUser({
  user: "admin",
  pwd: "your-strong-password",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})
exit
```

### Cấu hình MongoDB security:
```bash
sudo nano /etc/mongod.conf
```

Thêm vào file config:
```yaml
security:
  authorization: enabled
net:
  bindIp: 127.0.0.1
  port: 27017
```

## 3. Cấu hình ứng dụng

### Tạo file .env:
```bash
cp .env.example .env
nano .env
```

### Cập nhật các thông số trong .env:
```env
NODE_ENV=production
PORT=8080
MONGODB_URL=*********************************************
DB_NAME=CLUB3333
DB_AUTH_SOURCE=admin
ALLOWED_ORIGINS=https://yourdomain.com
SMS_URL=your-sms-provider-url
SMS_TOKEN=your-sms-token
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
```

## 4. Cài đặt ứng dụng

```bash
# Clone code và cài đặt dependencies
npm install

# Cài đặt helmet cho security
npm install helmet

# Khởi động ứng dụng với PM2
pm2 start server.js --name "go88-game"
pm2 startup
pm2 save
```

## 5. Cấu hình Nginx (Reverse Proxy)

### Cài đặt Nginx:
```bash
sudo apt-get install nginx
```

### Tạo config file:
```bash
sudo nano /etc/nginx/sites-available/go88-game
```

### Nội dung config:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Kích hoạt site:
```bash
sudo ln -s /etc/nginx/sites-available/go88-game /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 6. Cấu hình SSL (HTTPS)

### Sử dụng Let's Encrypt:
```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

## 7. Cấu hình Firewall

```bash
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## 8. Monitoring và Logs

### Xem logs ứng dụng:
```bash
pm2 logs go88-game
```

### Xem status:
```bash
pm2 status
```

### Restart ứng dụng:
```bash
pm2 restart go88-game
```

## 9. Backup Database

### Tạo script backup:
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --host localhost:27017 --db CLUB3333 --out /backup/mongodb_$DATE
```

## 10. Troubleshooting

### Kiểm tra port đang sử dụng:
```bash
netstat -tulpn | grep :8080
```

### Kiểm tra MongoDB connection:
```bash
mongo *************************************************
```

### Kiểm tra logs Nginx:
```bash
sudo tail -f /var/log/nginx/error.log
```
