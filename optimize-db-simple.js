// Simple MongoDB optimization without external dependencies
const mongoose = require('mongoose');

// Connect to database
mongoose.connect('mongodb://127.0.0.1:27017/CLUB3333', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    // Optimization settings
    maxPoolSize: 20,
    minPoolSize: 2,
    maxIdleTimeMS: 30000,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    connectTimeoutMS: 10000
});

async function optimizeDatabase() {
    try {
        console.log('🚀 Starting database optimization...\n');
        
        const db = mongoose.connection.db;
        
        // 1. Clean old chat messages (older than 7 days)
        console.log('🧹 Cleaning old chat messages...');
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const chatResult = await db.collection('txchats').deleteMany({
            time: { $lt: sevenDaysAgo }
        });
        console.log(`✅ Deleted ${chatResult.deletedCount} old chat messages`);
        
        // 2. Clean old OTP codes (older than 1 day)
        console.log('🧹 Cleaning old OTP codes...');
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const otpResult = await db.collection('otps').deleteMany({
            date: { $lt: oneDayAgo }
        });
        console.log(`✅ Deleted ${otpResult.deletedCount} old OTP codes`);
        
        // 3. Create essential indexes
        console.log('📊 Creating database indexes...');
        
        try {
            // UserInfo indexes
            await db.collection('userinfos').createIndex({ 'id': 1 }, { background: true });
            await db.collection('userinfos').createIndex({ 'name': 1 }, { background: true });
            console.log('✅ UserInfo indexes created');
            
            // TaiXiu indexes
            await db.collection('taixius').createIndex({ 'uid': 1, 'phien': 1 }, { background: true });
            await db.collection('taixius').createIndex({ 'time': -1 }, { background: true });
            console.log('✅ TaiXiu indexes created');
            
            // Chat indexes
            await db.collection('txchats').createIndex({ 'time': -1 }, { background: true });
            await db.collection('txchats').createIndex({ 'uid': 1 }, { background: true });
            console.log('✅ Chat indexes created');
            
        } catch (indexError) {
            console.log('⚠️  Some indexes already exist (this is normal)');
        }
        
        // 4. Get database statistics
        console.log('\n📈 Database statistics:');
        try {
            const stats = await db.stats();
            console.log(`Database size: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
            console.log(`Index size: ${(stats.indexSize / 1024 / 1024).toFixed(2)} MB`);
            console.log(`Collections: ${stats.collections}`);
            console.log(`Objects: ${stats.objects}`);
        } catch (statsError) {
            console.log('⚠️  Could not get database stats');
        }
        
        // 5. Check connection status
        console.log('\n🔗 Connection status:');
        console.log(`Ready state: ${mongoose.connection.readyState}`);
        console.log(`Host: ${mongoose.connection.host || 'localhost'}`);
        console.log(`Port: ${mongoose.connection.port || 27017}`);
        console.log(`Database: ${mongoose.connection.name || 'CLUB3333'}`);
        
        console.log('\n✅ Database optimization completed successfully!');
        console.log('🚀 Your server should now run faster on IP *************');
        
    } catch (error) {
        console.error('❌ Database optimization error:', error.message);
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Make sure MongoDB is running: net start MongoDB');
        console.log('2. Check if database name is correct: CLUB3333');
        console.log('3. Verify MongoDB is accessible on localhost:27017');
    } finally {
        // Close connection
        mongoose.connection.close();
        console.log('\n🔌 Database connection closed');
    }
}

// Run optimization
optimizeDatabase();
