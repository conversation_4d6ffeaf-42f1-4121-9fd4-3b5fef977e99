# Hướng dẫn tối ưu hóa hiệu suất cho IP Public *************

## 🚨 Vấn đề hiện tại:
- Server chạy chậm trên IP public so với localhost
- Lag và không hoạt động bình thường
- Kết nối WebSocket không ổn định

## 🎯 Nguyên nhân chính:

### 1. Database Performance
- ❌ Không có connection pooling
- ❌ Thiếu indexes
- ❌ Query không tối ưu
- ❌ Dữ liệu cũ không được dọn dẹp

### 2. Network & Server
- ❌ Không có compression
- ❌ Thiếu caching cho static files
- ❌ CORS không tối ưu
- ❌ Không có reverse proxy

### 3. Node.js Configuration
- ❌ Memory limit mặc định thấp
- ❌ Thread pool size không đủ
- ❌ Không có process clustering

## 🚀 Giải pháp đã triển khai:

### 1. Database Optimization
```javascript
// Connection pooling
maxPoolSize: 50,
minPoolSize: 5,
maxIdleTimeMS: 30000,
serverSelectionTimeoutMS: 5000,
socketTimeoutMS: 45000,
connectTimeoutMS: 10000
```

### 2. Server Performance
```javascript
// Gzip compression
app.use(compression({
    level: 6,
    threshold: 1024
}));

// Static file caching
maxAge: oneYear,
etag: true,
lastModified: true
```

### 3. CORS Optimization
```javascript
// Specific origins for production
allowedOrigins: ['http://*************:8080', 'http://*************']
maxAge: 86400 // Cache preflight requests
```

## 📋 Các bước tối ưu hóa:

### Bước 1: Chạy script tối ưu hóa
```batch
optimize-server.bat
```

### Bước 2: Tối ưu hóa MongoDB
```batch
node optimize-mongodb.js
```

### Bước 3: Cấu hình environment
```batch
copy .env.example .env
# Chỉnh sửa .env với thông số production
```

### Bước 4: Khởi động với PM2
```batch
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Bước 5: Cấu hình Nginx (Khuyến nghị)
```bash
# Copy nginx config
sudo cp nginx-config.conf /etc/nginx/sites-available/go88
sudo ln -s /etc/nginx/sites-available/go88 /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔧 Cấu hình hệ thống:

### Windows Server Optimization
```batch
# Tăng connection limit
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled

# Tối ưu memory
bcdedit /set increaseuserva 3072
```

### MongoDB Optimization
```javascript
// Tạo indexes quan trọng
db.userinfos.createIndex({ "id": 1 })
db.userinfos.createIndex({ "name": 1 })
db.taixius.createIndex({ "uid": 1, "phien": 1 })
db.txchats.createIndex({ "time": -1 })

// Dọn dẹp dữ liệu cũ
db.txchats.deleteMany({ "time": { $lt: new Date(Date.now() - 7*24*60*60*1000) } })
db.otps.deleteMany({ "date": { $lt: new Date(Date.now() - 24*60*60*1000) } })
```

## 📊 Monitoring & Debugging:

### PM2 Monitoring
```batch
pm2 monit          # Real-time monitoring
pm2 logs           # View logs
pm2 restart all    # Restart all processes
pm2 reload all     # Zero-downtime reload
```

### Performance Metrics
```batch
# Memory usage
pm2 show go88-game

# CPU usage
wmic process where name="node.exe" get processid,percentprocessortime

# Network connections
netstat -an | find "8080"
```

### Database Performance
```javascript
// Check slow queries
db.setProfilingLevel(2, { slowms: 100 })
db.system.profile.find().sort({ ts: -1 }).limit(5)

// Index usage stats
db.userinfos.aggregate([{ $indexStats: {} }])
```

## 🎯 Kết quả mong đợi:

### Before Optimization:
- ❌ Response time: 2-5 seconds
- ❌ WebSocket lag: 1-2 seconds
- ❌ Memory usage: High
- ❌ CPU usage: 80-100%

### After Optimization:
- ✅ Response time: 200-500ms
- ✅ WebSocket lag: <100ms
- ✅ Memory usage: Optimized
- ✅ CPU usage: 30-50%

## 🔍 Troubleshooting:

### Vẫn chậm sau optimization:
1. **Kiểm tra network latency:**
   ```batch
   ping *************
   tracert *************
   ```

2. **Kiểm tra server resources:**
   ```batch
   wmic computersystem get TotalPhysicalMemory
   wmic cpu get loadpercentage
   ```

3. **Kiểm tra MongoDB performance:**
   ```javascript
   db.stats()
   db.serverStatus().connections
   ```

### WebSocket vẫn lag:
1. **Kiểm tra firewall:**
   ```batch
   netsh advfirewall firewall show rule name="Node.js"
   ```

2. **Kiểm tra proxy settings:**
   - Đảm bảo Nginx cấu hình đúng WebSocket
   - Kiểm tra timeout settings

3. **Kiểm tra client-side:**
   - Clear browser cache
   - Disable browser extensions
   - Test từ different networks

## 📈 Advanced Optimizations:

### 1. Redis Caching (Optional)
```javascript
const redis = require('redis');
const client = redis.createClient();

// Cache user sessions
app.use(session({
    store: new RedisStore({ client: client }),
    secret: 'your-secret-key'
}));
```

### 2. CDN Setup (Optional)
- CloudFlare for static assets
- AWS CloudFront
- Local CDN server

### 3. Load Balancing (Optional)
```nginx
upstream go88_cluster {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}
```

## ✅ Checklist hoàn thành:

- [ ] Chạy optimize-server.bat
- [ ] Tối ưu hóa MongoDB
- [ ] Cấu hình .env file
- [ ] Khởi động với PM2
- [ ] Cấu hình Nginx (nếu có)
- [ ] Test performance
- [ ] Monitor logs
- [ ] Backup database

**Server đã được tối ưu hóa cho production!** 🚀
