@echo off
title GO88 Server Optimization Tool
color 0B
echo.
echo ========================================
echo     GO88 SERVER OPTIMIZATION TOOL
echo ========================================
echo.

echo [1/8] Installing performance dependencies...
echo.

REM Install compression middleware
npm install compression --save
if errorlevel 1 (
    echo [WARNING] Failed to install compression
) else (
    echo [OK] Compression installed
)

REM Install memory cache
npm install memory-cache --save
if errorlevel 1 (
    echo [WARNING] Failed to install memory-cache
) else (
    echo [OK] Memory cache installed
)

echo.
echo [2/8] Optimizing MongoDB...
echo.
node optimize-mongodb.js

echo.
echo [3/8] Checking system resources...
echo.

REM Check available memory
for /f "tokens=2 delims=:" %%a in ('systeminfo ^| find "Available Physical Memory"') do (
    echo Available RAM: %%a
)

REM Check CPU usage
wmic cpu get loadpercentage /value | find "LoadPercentage"

echo.
echo [4/8] Optimizing Node.js settings...
echo.

REM Set Node.js memory limit (adjust based on available RAM)
set NODE_OPTIONS=--max-old-space-size=2048

REM Set UV thread pool size for better I/O performance
set UV_THREADPOOL_SIZE=16

echo [OK] Node.js environment optimized

echo.
echo [5/8] Checking network configuration...
echo.

REM Check if port 8080 is available
netstat -an | find "8080" | find "LISTENING" >nul
if not errorlevel 1 (
    echo [WARNING] Port 8080 is already in use
    echo [INFO] Killing existing processes...
    for /f "tokens=5" %%a in ('netstat -ano ^| find "8080" ^| find "LISTENING"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
)

echo [OK] Port 8080 is available

echo.
echo [6/8] Setting up process monitoring...
echo.

REM Check if PM2 is installed
pm2 --version >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing PM2 for process management...
    npm install -g pm2
    if errorlevel 1 (
        echo [WARNING] Failed to install PM2
    ) else (
        echo [OK] PM2 installed successfully
    )
) else (
    echo [OK] PM2 is already installed
)

echo.
echo [7/8] Creating optimized startup script...
echo.

REM Create PM2 ecosystem file
echo module.exports = { > ecosystem.config.js
echo   apps: [{ >> ecosystem.config.js
echo     name: 'go88-game', >> ecosystem.config.js
echo     script: 'server.js', >> ecosystem.config.js
echo     instances: 'max', >> ecosystem.config.js
echo     exec_mode: 'cluster', >> ecosystem.config.js
echo     env: { >> ecosystem.config.js
echo       NODE_ENV: 'production', >> ecosystem.config.js
echo       PORT: 8080 >> ecosystem.config.js
echo     }, >> ecosystem.config.js
echo     max_memory_restart: '1G', >> ecosystem.config.js
echo     error_file: './logs/err.log', >> ecosystem.config.js
echo     out_file: './logs/out.log', >> ecosystem.config.js
echo     log_file: './logs/combined.log', >> ecosystem.config.js
echo     time: true >> ecosystem.config.js
echo   }] >> ecosystem.config.js
echo }; >> ecosystem.config.js

echo [OK] PM2 ecosystem file created

echo.
echo [8/8] Final optimizations...
echo.

REM Create logs directory
if not exist "logs" mkdir logs

REM Set production environment
set NODE_ENV=production

echo [OK] Environment configured for production

echo.
echo ========================================
echo OPTIMIZATION COMPLETED!
echo ========================================
echo.
echo Next steps:
echo 1. Start with PM2: pm2 start ecosystem.config.js
echo 2. Monitor: pm2 monit
echo 3. Setup Nginx reverse proxy (see nginx-config.conf)
echo 4. Configure firewall and security
echo.
echo Performance improvements:
echo ✅ Database connection pooling
echo ✅ Gzip compression enabled
echo ✅ Static file caching
echo ✅ Memory optimization
echo ✅ Process clustering with PM2
echo ✅ MongoDB indexes and cleanup
echo.
pause
