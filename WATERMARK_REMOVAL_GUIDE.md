# Hướng dẫn xóa Watermark "SOURCE SHARE BY NXT TELEGRAM @setupgamebai"

## 🎯 Mục đích
Xóa dòng chữ watermark "SOURCE SHARE BY NXT TELEGRAM @setupgamebai" hiển thị trên game.

## 📍 Vị trí có thể chứa watermark:

### 1. Database (Thông báo hệ thống)
- **Collection**: `ThongBao` trong MongoDB
- **Fields**: `thongbao1`, `thongbao2`, `thongbao3`, `hienthitb`
- **Hiển thị**: Thông báo chạy trên game

### 2. Game Client (Cocos2d-js)
- **Files**: `public/main.*.js`, `public/src/project.*.js`
- **Hiển thị**: Hardcode trong game client

### 3. Server-side Templates
- **Files**: `views/*.ejs`, CSS files
- **Hiển thị**: Trên web interface

## 🛠️ C<PERSON>ch xóa watermark:

### Phương pháp 1: Sử dụng Tool tự động (<PERSON><PERSON><PERSON>ến nghị)

```batch
# Chạy tool tự động
remove-watermark.bat
```

**Tool sẽ:**
1. Kiểm tra MongoDB connection
2. Tìm kiếm watermark trong database
3. Xóa và thay thế bằng text mới
4. Hiển thị kết quả

### Phương pháp 2: Kiểm tra thủ công

```batch
# Chỉ kiểm tra, không xóa
node check-watermark.js
```

### Phương pháp 3: Xóa thủ công trong database

```javascript
// Kết nối MongoDB
mongo
use CLUB3333

// Xem tất cả thông báo
db.thongbaos.find()

// Xóa watermark và thay thế
db.thongbaos.updateMany(
    { "thongbao1": /setupgamebai/i },
    { $set: { "thongbao1": "GO88 Game xanh chín uy tín" } }
)

db.thongbaos.updateMany(
    { "thongbao2": /NXT.*TELEGRAM/i },
    { $set: { "thongbao2": "Chào mừng bạn đến với GO88!" } }
)

db.thongbaos.updateMany(
    { "hienthitb": /SOURCE.*SHARE/i },
    { $set: { "hienthitb": "GO88 - Game bài đổi thưởng uy tín" } }
)
```

## 🔍 Các vị trí khác có thể chứa watermark:

### 1. Kiểm tra trong game client
```batch
# Tìm trong file JavaScript
findstr /s /i "setupgamebai" public\*.js
findstr /s /i "NXT" public\*.js
```

### 2. Kiểm tra trong CSS
```batch
# Tìm trong file CSS
findstr /s /i "SOURCE" *.css
```

### 3. Kiểm tra trong templates
```batch
# Tìm trong file EJS
findstr /s /i "setupgamebai" views\*.ejs
```

## 📝 Text thay thế được sử dụng:

- **thongbao1**: "GO88 Game xanh chín uy tín"
- **thongbao2**: "Chào mừng bạn đến với GO88!"
- **thongbao3**: "Game bài đổi thưởng uy tín số 1"
- **hienthitb**: "GO88 - Game bài đổi thưởng uy tín"

## ⚠️ Lưu ý quan trọng:

### 1. Backup trước khi xóa
```batch
# Backup database
mongodump --db CLUB3333 --out backup_before_remove_watermark
```

### 2. Restart server sau khi xóa
```batch
# Dừng server
Ctrl+C

# Khởi động lại
start.bat
```

### 3. Clear cache browser
- Nhấn `Ctrl+F5` để refresh cache
- Hoặc clear browser cache

## 🚨 Troubleshooting:

### Lỗi: "Cannot connect to MongoDB"
```batch
# Khởi động MongoDB
net start MongoDB

# Hoặc
mongod --dbpath C:\data\db
```

### Lỗi: "Cannot find module"
```batch
# Cài đặt dependencies
npm install mongoose
```

### Watermark vẫn hiển thị sau khi xóa
1. **Kiểm tra cache**: Clear browser cache
2. **Kiểm tra game client**: Watermark có thể hardcode trong file JS
3. **Kiểm tra CSS**: Có thể được inject qua CSS
4. **Restart server**: Đảm bảo server đã restart

## 📋 Checklist sau khi xóa:

- [ ] Chạy `check-watermark.js` để xác nhận đã xóa
- [ ] Restart server
- [ ] Clear browser cache
- [ ] Test game để xem watermark đã biến mất
- [ ] Kiểm tra thông báo hệ thống trong game
- [ ] Kiểm tra trang chủ và các trang khác

## 🎯 Kết quả mong đợi:

✅ **Trước**: "SOURCE SHARE BY NXT TELEGRAM @setupgamebai"
✅ **Sau**: "GO88 - Game bài đổi thưởng uy tín"

## 📞 Hỗ trợ:

Nếu watermark vẫn hiển thị sau khi làm theo hướng dẫn:
1. Chụp screenshot vị trí hiển thị watermark
2. Chạy `check-watermark.js` và gửi kết quả
3. Kiểm tra console browser có lỗi gì không

**Watermark đã được xóa thành công!** 🎉
