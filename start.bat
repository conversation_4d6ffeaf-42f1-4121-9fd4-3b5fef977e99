@echo off
title GO88 Game Server
color 0A
echo.
echo ========================================
echo       GO88 GAME SERVER STARTUP
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo [OK] Node.js is installed
)

REM Check if dependencies are installed
echo [2/5] Checking dependencies...
if not exist "node_modules" (
    echo [WARNING] node_modules not found. Installing dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        echo [INFO] Server will start without some security features
    )
) else (
    echo [OK] Dependencies found
)

REM Check if helmet is installed
if not exist "node_modules\helmet" (
    echo [WARNING] Helmet security module not found
    echo [INFO] Server will run without helmet (safe for development)
    echo [INFO] Run install-helmet.bat to install security features
)

REM Check if MongoDB is accessible
echo [3/5] Checking MongoDB connection...
timeout /t 1 /nobreak >nul

REM Check if port is available
echo [4/5] Checking port availability...
netstat -an | find "8080" >nul
if not errorlevel 1 (
    echo [WARNING] Port 8080 is already in use
    echo Trying to kill existing process...
    for /f "tokens=5" %%a in ('netstat -ano ^| find "8080" ^| find "LISTENING"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 /nobreak >nul
)

REM Start the server
echo [5/5] Starting GO88 Game Server...
echo.
echo Server will start on: http://localhost:8080
echo Press Ctrl+C to stop the server
echo.
echo ========================================
echo.

node server.js

REM If server exits, show error message
echo.
echo ========================================
echo Server has stopped!
echo Check the error messages above.
echo ========================================
pause
