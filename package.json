{"name": "RedVip", "version": "1.17.02", "description": "Server game By MrT98 (Red T)", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"all": "^0.0.0", "bcrypt": "^5.0.1", "bcrypt-nodejs": "0.0.3", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cloudscraper": "^4.1.2", "cors": "^2.8.5", "cron": "^1.7.2", "crypto-js": "^4.1.1", "dateformat": "^4.5.1", "dotenv": "^8.1.0", "ejs": "^2.7.1", "excel4node": "^1.7.2", "express": "^4.17.1", "express-ws": "^4.0.0", "format-currency": "^1.0.0", "helmet": "^4.6.0", "is-mobile": "^2.1.0", "jsonwebtoken": "^8.5.1", "mongoose": "^5.12.7", "mongoose-auto-increment-reworked": "^1.2.1", "mongoose-long": "^0.2.1", "morgan": "^1.9.1", "node-telegram-bot-api": "^0.30.0", "node-tmux": "^1.0.2", "passport": "^0.4.1", "request": "^2.88.0", "saslprep": "^1.0.3", "shortid": "^2.2.15", "svg-captcha": "^1.4.0", "svg2img": "^0.9.2", "telebot": "1.4.1", "validator": "^11.1.0", "voucher-code-generator": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/MrT98/RedVip.git"}, "homepage": "https://github.com/MrT98/RedVip", "bugs": {"url": "https://github.com/MrT98/RedVip/issues"}, "author": {"name": "Red T", "email": "<EMAIL>", "url": "https://github.com/MrT98"}, "license": "ISC"}