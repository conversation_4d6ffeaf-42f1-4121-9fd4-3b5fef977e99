
let BanCa = function(){
	this.wait1 = {};
	this.wait2 = {};
	this.wait3 = {};

	this.bet = {
		1:{0:100, 1:200,  2:300,  3:500,  4:700,  5:1000},
		2:{0:500, 1:1000, 2:1500, 3:2500, 4:3500, 5:5000},
		3:{0:1000,1:2000, 2:3000, 3:5000, 4:7000, 5:10000},
	};

	this.fish = {
		1: {'b':2,   'max':2,   'min':1,   'clip':14},
		2: {'b':2,   'max':2,   'min':1,   'clip':1},
		3: {'b':3,   'max':4,   'min':1,   'clip':3},
		4: {'b':4,   'max':5,   'min':2,   'clip':4},
		5: {'b':4,   'max':5,   'min':2,   'clip':4},
		6: {'b':4,   'max':5,   'min':2,   'clip':5},
		7: {'b':5,   'max':6,   'min':3,   'clip':18},
		8: {'b':5,   'max':6,   'min':3,   'clip':18},
		9: {'b':8,   'max':10,  'min':4,   'clip':14},
		10:{'b':10,  'max':13,  'min':5,   'clip':14},
		11:{'b':10,  'max':13,  'min':5,   'clip':14},
		12:{'b':12,  'max':16,  'min':5,   'clip':14},
		13:{'b':15,  'max':20,  'min':5,   'clip':14},
		14:{'b':15,  'max':20,  'min':5,   'clip':14},
		15:{'b':15,  'max':20,  'min':5,   'clip':14},
		16:{'b':30,  'max':45,  'min':5,   'clip':14},
		17:{'b':40,  'max':70,  'min':5,   'clip':14},
		18:{'b':50,  'max':80,  'min':5,   'clip':14},
		19:{'b':50,  'max':80,  'min':5,   'clip':14},
		20:{'b':60,  'max':92,  'min':20,  'clip':14},
		21:{'b':80,  'max':115, 'min':30,  'clip':16},
		22:{'b':90,  'max':120, 'min':30,  'clip':16},
		23:{'b':100, 'max':130, 'min':50,  'clip':14},
		24:{'b':120, 'max':140, 'min':80,  'clip':14},
		25:{'b':130, 'max':145, 'min':90,  'clip':10},
		26:{'b':150, 'max':175, 'min':100, 'clip':8},
		27:{'b':200, 'max':260, 'min':100, 'clip':6},
	};
	this.group = {
		'1': {'g':'2_2g6', 'z':2, 'f':[2,2,2,2,2,2], 'clip':11},
		'20':{'g':'r1', 'f':[16,16,9,9,9,9,9,9,9,9,2,2,2,2,2,2,6,6,6,2,2,2,2,2,2,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,18,18,18], 'clip':2, 't':54},
		'21':{'g':'r2', 'f':[8,8,3,3,3,3,10,8,8,3,3,5,5,11,9,9,9,9,6,6,7,7,7,7,7,7,12,6,6,6,6,6,6,10,10,12], 'clip':2, 't':54},
		'22':{'g':'r3', 'f':[3,3,3,3,3,3,3,3,3,5,5,5,5,5,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,5,5,5,5,5,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,5,5,5,5,5,3,3,3,3,3,3,3], 'clip':2, 't':55},
	};
}

BanCa.prototype.addWait = function(wait, room){
	this['wait'+wait][room.id] = room;
	return room;
}

BanCa.prototype.removeWait = function(wait, id){
	delete this['wait'+wait][id];
}

module.exports = BanCa;
