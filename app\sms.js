
/**
 * SMS Controller
 */

let request = require('request');
let config  = require('../config/sms');

let sendOTP = function(phone, otp, callback){
	let form = {
		  'source': 'Verify',
		  'destination': phone,
		  'text': 'Verification Code: ' + otp,
		  'encoding': 'AUTO',
	};
	request.post({
		url: config.URL,
		headers: {'Authorization':'Bearer ' + config.Author, 'Content-Type': 'application/json'},
		json: form,
	}, function(error, response, body) {
		if (callback) {
			if (error) {
				console.log('SMS Error:', error);
				callback(false, error);
			} else {
				console.log('SMS Response:', body);
				callback(true, body);
			}
		}
	});
}

module.exports = {
	sendOTP: sendOTP,
}
