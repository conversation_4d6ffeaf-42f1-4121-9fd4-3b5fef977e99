
module.exports = {
	'extract':      0.8,                                         // Chiết khẩu 0%
	//'URL':          'http://gakon.club:10004/api/SIM/RegCharge?', // URL API
	//'APP_ID':       '3297924361',                              // id app
    //'APP_PASSWORD': 'b4056f1b7b4e0664182fea5124f40082',        // pass app
	//'api_key': '3b83e92f-89c4-41ca-b9b6-eaaec0d807cd',
    '1': 'Đang nạp...',
    '1': 'Nạp thẻ cào thành công.',
    '0': 'Thẻ cào sai mệnh giá.',
    '0': 'Thẻ cào không đúng hoặc đã qua sử dụng.',
    '0': '<PERSON><PERSON> thống đang bảo trì, giữ lại thẻ và quay lại sau.',
    '0': 'Thẻ sai định dạng.',

/**
    '00': '<PERSON>ạ<PERSON> thẻ cào thành công.',
	'99': '<PERSON><PERSON> thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'01': '<PERSON><PERSON> thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'02': 'Dữ liệu gửi lên không hợp lệ.',
	'03': 'Hệ thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'04': 'Hệ thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'05': 'Hệ thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'06': 'Hệ thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'07': 'Thẻ cào đã qua sử dụng.',
	'08': 'Thẻ đã bị khóa.',
	'09': 'Thẻ hết đã hạn sử dụng.',
	'10': 'Thẻ không tồn tại.',
	'11': 'Mã thẻ sai định dạng.',
	'12': 'Sai số serial của thẻ.',
	'13': 'Mã thẻ và số serial không khớp.',
	'14': 'Thẻ không tồn tại.',
	'15': 'Thẻ không sử dụng được.',
	'16': 'Nhập sai quá giới hạn cho phép.',
	'17': 'Hệ thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'18': 'Đang nạp...',
	'19': 'Không thể kết nối tới máy chủ.',
	'20': 'Đã gửi yêu cầu nạp thẻ.',
	'21': 'Hệ thống đang bảo trì, giữ lại thẻ và quay lại sau.',
	'22': 'Mệnh giá thẻ không hợp lệ.',
	*/
};
